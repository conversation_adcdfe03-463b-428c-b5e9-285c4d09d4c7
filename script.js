document.addEventListener('DOMContentLoaded', () => {
    // --- Supabase Setup ---
    const SUPABASE_URL = 'https://znidotclrjksnizomrmh.supabase.co';
    const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpuaWRvdGNscmprc25pem9tcm1oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQzMjM0OTYsImV4cCI6MjA2OTg5OTQ5Nn0.16oxptca7i9l_kO6mvAlc8ProhQ5UfhQ9RToShzvm5M';
    const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

    // --- Element Getters ---
    const getVideoPlayer = () => document.getElementById('video-player');
    const getM3uInput = () => document.getElementById('m3u-input');
    const getLoadStreamBtn = () => document.getElementById('load-stream-btn');
    const getStreamDropdown = () => document.getElementById('stream-dropdown');
    const getDanmakuInput = () => document.getElementById('danmaku-input');
    const getSendDanmakuBtn = () => document.getElementById('send-danmaku-btn');
    const getDanmakuOverlay = () => document.querySelector('.danmaku-overlay');
    const getLoginBtn = () => document.getElementById('login-btn');
    const getLogoutBtn = () => document.getElementById('logout-btn');
    const getUserInfo = () => document.getElementById('user-info');
    const getContributeBtn = () => document.getElementById('contribute-btn');
    const getScheduleBtn = () => document.getElementById('schedule-btn');
    const getInfoModal = () => document.getElementById('info-modal');
    const getAuthModal = () => document.getElementById('auth-modal');

    let hls = null;

    // --- Toast Notification Function ---
    function showToast(message, type = 'info') {
        // 移除已存在的 toast
        const existingToast = document.querySelector('.toast');
        if (existingToast) {
            existingToast.remove();
        }

        // 创建 toast 元素
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;

        // 添加到页面
        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => toast.classList.add('show'), 100);

        // 自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    // --- HLS Video Loading Logic ---
    function loadStream(sourceUrl) {
        const video = getVideoPlayer();
        if (!video) return;
        if (!sourceUrl) {
            showToast('请输入有效的 M3U/M3U8 链接', 'warning');
            return;
        }
        if (Hls.isSupported()) {
            if (hls) hls.destroy();
            hls = new Hls();
            hls.loadSource(sourceUrl);
            hls.attachMedia(video);
            hls.on(Hls.Events.MANIFEST_PARSED, () => {
                video.play().catch(e => console.error("播放失败：", e));
                localStorage.setItem('lastStreamUrl', sourceUrl);
            });
            hls.on(Hls.Events.ERROR, (_, data) => {
                if (data.fatal) {
                    console.error('HLS 加载发生致命错误:', data);
                    showToast('视频源加载失败，请检查链接或更换线路', 'error');
                }
            });
        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
            video.src = sourceUrl;
            video.addEventListener('loadedmetadata', () => {
                video.play().catch(e => console.error("播放失败：", e));
                localStorage.setItem('lastStreamUrl', sourceUrl);
            });
        } else {
            showToast('您的浏览器不支持 HLS 播放', 'error');
        }
    }

    // --- Danmaku Logic ---
    async function sendDanmaku() {
        const danmakuInput = getDanmakuInput();
        if (!danmakuInput) return;
        const content = danmakuInput.value.trim();
        if (!content) return;
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
            const authModal = getAuthModal();
            if (authModal) authModal.style.display = 'flex';
            return;
        }
        const { error } = await supabase.from('danmaku').insert({ content: content, user_id: user.id });
        if (error) {
            console.error('发送弹幕失败:', error);
            showToast('弹幕发送失败，请稍后重试', 'error');
        } else {
            danmakuInput.value = '';
            showToast('弹幕发送成功', 'success');
        }
    }

    function displayDanmaku(danmaku) {
        const danmakuOverlay = getDanmakuOverlay();
        if (!danmakuOverlay) return;
        const danmakuElement = document.createElement('div');
        danmakuElement.classList.add('danmaku');
        danmakuElement.textContent = danmaku.content;
        const randomTop = Math.floor(Math.random() * 80) + 5;
        danmakuElement.style.top = `${randomTop}%`;
        danmakuOverlay.appendChild(danmakuElement);
        danmakuElement.addEventListener('animationend', () => danmakuElement.remove());
    }

    function subscribeToDanmaku() {
        try {
            supabase.channel('public:danmaku')
                .on('postgres_changes', { event: 'INSERT', schema: 'public', table: 'danmaku' }, payload => {
                    displayDanmaku(payload.new);
                })
                .subscribe();
        } catch (e) {
            console.error("订阅弹幕失败: ", e);
        }
    }

    // --- Stream Contribution & Selection Logic ---
    async function fetchAndDisplayStreams() {
        const streamDropdown = getStreamDropdown();
        if (!streamDropdown) return;
        const { data, error } = await supabase.from('streams').select('name, url').eq('status', 'approved');
        if (error) {
            console.error('获取社区线路失败:', error);
            return;
        }
        if (!data) return;
        while (streamDropdown.options.length > 1) streamDropdown.remove(1);
        data.forEach(stream => {
            const option = document.createElement('option');
            option.value = stream.url;
            option.textContent = stream.name;
            streamDropdown.appendChild(option);
        });
    }

    async function handleContributeStream() {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
            showToast('请先登录后再贡献线路', 'warning');
            const authModal = getAuthModal();
            if (authModal) authModal.style.display = 'flex';
            return;
        }
        const name = prompt("请输入线路名称 (例如: 高清线路1):");
        if (!name) return;
        const url = prompt("请输入 M3U/M3U8 链接地址:");
        if (!url) return;
        if (!url.startsWith('http') || !url.includes('.m3u')) {
            showToast('请输入一个有效的 M3U/M3U8 链接', 'warning');
            return;
        }
        const { error } = await supabase.from('streams').insert({ name: name, url: url, submitter_id: user.id });
        if (error) {
            showToast('提交失败，请稍后重试', 'error');
        } else {
            showToast('提交成功！感谢您的贡献，我们将会尽快审核', 'success');
        }
    }

    // --- F1 Info Modal Logic ---
    function renderTable(containerId, headers, data) {
        const container = document.getElementById(containerId);
        if (!container) return;
        let tableHtml = '<table><thead><tr>';
        headers.forEach(h => tableHtml += `<th>${h}</th>`);
        tableHtml += '</tr></thead><tbody>';
        data.forEach(row => {
            tableHtml += '<tr>';
            Object.values(row).forEach(val => tableHtml += `<td>${val}</td>`);
            tableHtml += '</tr>';
        });
        tableHtml += '</tbody></table>';
        container.innerHTML = tableHtml;
    }

    async function fetchF1Data() {
        try {
            const tables = ['schedule', 'driver_standings', 'constructor_standings'];
            const [scheduleData, driverData, constructorData] = await Promise.all(
                tables.map(table => supabase.from(table).select('*'))
            );
            if (scheduleData.data) renderTable('schedule-content', ['赛事', '地点', '日期', '时间'], scheduleData.data.map(r => ({ name: r.race_name, loc: r.location, date: r.race_date, time: r.race_time })));
            if (driverData.data) renderTable('driver-standings-content', ['排名', '车手', '车队', '积分'], driverData.data.map(r => ({ pos: r.position, name: r.driver_name, team: r.team_name, pts: r.points })).sort((a, b) => a.pos - b.pos));
            if (constructorData.data) renderTable('constructor-standings-content', ['排名', '车队', '积分'], constructorData.data.map(r => ({ pos: r.position, team: r.team_name, pts: r.points })).sort((a, b) => a.pos - b.pos));
        } catch (e) {
            console.error("获取F1数据失败: ", e);
        }
    }

    // --- System Configuration ---
    let systemConfig = {
        requireInvitationCode: true // 默认需要邀请码
    };

    // --- Turnstile Configuration ---
    let turnstileToken = null;

    // Turnstile 回调函数
    window.onTurnstileSuccess = function(token) {
        turnstileToken = token;
        console.log('Turnstile 验证成功');
    };

    window.onTurnstileExpired = function() {
        turnstileToken = null;
        console.log('Turnstile 验证已过期');
    };

    window.onTurnstileError = function(error) {
        turnstileToken = null;
        console.error('Turnstile 验证错误:', error);
        showToast('人机验证失败，请刷新页面重试', 'error');
    };

    // 重置 Turnstile
    function resetTurnstile() {
        turnstileToken = null;
        if (window.turnstile) {
            const turnstileElement = document.querySelector('.cf-turnstile');
            if (turnstileElement) {
                window.turnstile.reset(turnstileElement);
            }
        }
    }

    async function loadSystemConfig() {
        try {
            const { data, error } = await supabase
                .from('system_config')
                .select('config_key, config_value')
                .in('config_key', ['require_invitation_code']);

            if (!error && data) {
                data.forEach(config => {
                    if (config.config_key === 'require_invitation_code') {
                        systemConfig.requireInvitationCode = config.config_value === 'true';
                    }
                });

                updateRegistrationUI();
            }
        } catch (error) {
            console.error('加载系统配置失败:', error);
            // 失败时使用默认配置（需要邀请码）
        }
    }

    function updateRegistrationUI() {
        const inviteCodeInput = document.getElementById('signup-invite-code');

        if (inviteCodeInput) {
            if (systemConfig.requireInvitationCode) {
                inviteCodeInput.style.display = 'block';
                inviteCodeInput.required = true;
                inviteCodeInput.placeholder = '邀请码';
            } else {
                inviteCodeInput.style.display = 'none';
                inviteCodeInput.required = false;
                inviteCodeInput.value = ''; // 清空值
            }
        }
    }

    // --- Auth Logic & Modal Handling ---
    function setupAuth() {
        const authModal = getAuthModal();
        if (!authModal) return;

        const loginView = document.getElementById('login-view');
        const signupView = document.getElementById('signup-view');
        const resetView = document.getElementById('reset-view');

        function showAuthView(view) {
            if (loginView) loginView.style.display = 'none';
            if (signupView) signupView.style.display = 'none';
            if (resetView) resetView.style.display = 'none';

            // 清空所有消息
            const loginMessage = document.getElementById('login-message');
            const signupMessage = document.getElementById('signup-message');
            const resetMessage = document.getElementById('reset-message');

            if(loginMessage) {
                loginMessage.textContent = '';
                loginMessage.className = 'auth-message';
            }
            if(signupMessage) {
                signupMessage.textContent = '';
                signupMessage.className = 'auth-message';
            }
            if(resetMessage) {
                resetMessage.textContent = '';
                resetMessage.className = 'auth-message';
            }

            // 清空表单输入
            if (view === loginView) {
                const loginEmail = document.getElementById('login-email');
                const loginPassword = document.getElementById('login-password');
                if (loginEmail) loginEmail.value = '';
                if (loginPassword) loginPassword.value = '';
            } else if (view === signupView) {
                const signupEmail = document.getElementById('signup-email');
                const signupPassword = document.getElementById('signup-password');
                const signupInviteCode = document.getElementById('signup-invite-code');
                if (signupEmail) signupEmail.value = '';
                if (signupPassword) signupPassword.value = '';
                if (signupInviteCode) signupInviteCode.value = '';
            } else if (view === resetView) {
                const resetEmail = document.getElementById('reset-email');
                if (resetEmail) resetEmail.value = '';
            }

            if (view) view.style.display = 'block';
        }

        const loginBtn = getLoginBtn();
        if(loginBtn) {
            loginBtn.addEventListener('click', () => {
                authModal.style.display = 'flex';
                showAuthView(loginView);
            });
        }

        const authCloseBtn = document.getElementById('auth-close-btn');
        if(authCloseBtn) {
            authCloseBtn.addEventListener('click', () => {
                authModal.style.display = 'none';
                // 清空所有表单和消息
                clearAllAuthForms();
            });
        }

        // 移除点击背景关闭模态框的功能，只能通过关闭按钮关闭

        // 添加清空所有认证表单的函数
        function clearAllAuthForms() {
            // 清空登录表单
            const loginEmail = document.getElementById('login-email');
            const loginPassword = document.getElementById('login-password');
            const loginMessage = document.getElementById('login-message');
            if (loginEmail) loginEmail.value = '';
            if (loginPassword) loginPassword.value = '';
            if (loginMessage) {
                loginMessage.textContent = '';
                loginMessage.className = 'auth-message';
            }

            // 清空注册表单
            const signupEmail = document.getElementById('signup-email');
            const signupPassword = document.getElementById('signup-password');
            const signupInviteCode = document.getElementById('signup-invite-code');
            const signupMessage = document.getElementById('signup-message');
            if (signupEmail) signupEmail.value = '';
            if (signupPassword) signupPassword.value = '';
            if (signupInviteCode) signupInviteCode.value = '';
            if (signupMessage) {
                signupMessage.textContent = '';
                signupMessage.className = 'auth-message';
            }

            // 清空重置密码表单
            const resetEmail = document.getElementById('reset-email');
            const resetMessage = document.getElementById('reset-message');
            if (resetEmail) resetEmail.value = '';
            if (resetMessage) {
                resetMessage.textContent = '';
                resetMessage.className = 'auth-message';
            }

            // 重置 Turnstile
            resetTurnstile();
        }

        const showSignupBtn = document.getElementById('show-signup');
        if(showSignupBtn) {
            showSignupBtn.addEventListener('click', (e) => { e.preventDefault(); showAuthView(signupView); });
        }

        const showLoginBtn = document.getElementById('show-login');
        if(showLoginBtn) {
            showLoginBtn.addEventListener('click', (e) => { e.preventDefault(); showAuthView(loginView); });
        }
        
        const showResetBtn = document.getElementById('show-reset');
        if(showResetBtn) {
            showResetBtn.addEventListener('click', (e) => { e.preventDefault(); showAuthView(resetView); });
        }

        const showLoginFromResetBtn = document.getElementById('show-login-from-reset');
        if(showLoginFromResetBtn) {
            showLoginFromResetBtn.addEventListener('click', (e) => { e.preventDefault(); showAuthView(loginView); });
        }

        const loginForm = document.getElementById('login-form');
        if(loginForm) {
            loginForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                const email = document.getElementById('login-email').value.trim();
                const password = document.getElementById('login-password').value;
                const loginMessage = document.getElementById('login-message');

                // 前端基本验证
                if (!email) {
                    loginMessage.textContent = '请输入邮箱地址';
                    loginMessage.className = 'auth-message error';
                    return;
                }

                if (!password) {
                    loginMessage.textContent = '请输入密码';
                    loginMessage.className = 'auth-message error';
                    return;
                }

                const { data, error } = await supabase.auth.signInWithPassword({ email, password });
                if (error) {
                    const errorMsg = error.message.toLowerCase();

                    if (errorMsg.includes('invalid login credentials') || errorMsg.includes('invalid email or password')) {
                        loginMessage.textContent = '登录失败，邮箱或密码错误';
                    } else if (errorMsg.includes('email not confirmed')) {
                        loginMessage.textContent = '请先确认邮箱后再登录';
                    } else if (errorMsg.includes('too many requests') || errorMsg.includes('rate limit')) {
                        loginMessage.textContent = '登录尝试过于频繁，请稍后重试';
                    } else if (errorMsg.includes('user not found')) {
                        loginMessage.textContent = '用户不存在，请检查邮箱地址';
                    } else if (errorMsg.includes('signup disabled')) {
                        loginMessage.textContent = '账户功能已被禁用，请联系管理员';
                    } else {
                        loginMessage.textContent = `登录失败：${error.message}`;
                    }
                    loginMessage.className = 'auth-message error';
                } else {
                    showToast('登录成功！', 'success');
                    authModal.style.display = 'none';
                    clearAllAuthForms(); // 清空表单
                    updateUserUI(data.user);
                }
            });
        }

        const signupForm = document.getElementById('signup-form');
        if(signupForm) {
            signupForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                const email = document.getElementById('signup-email').value.trim();
                const password = document.getElementById('signup-password').value;
                const inviteCode = document.getElementById('signup-invite-code').value.trim();
                const signupMessage = document.getElementById('signup-message');

                // 前端基本验证
                if (!email) {
                    signupMessage.textContent = '请输入邮箱地址';
                    signupMessage.className = 'auth-message error';
                    return;
                }

                if (!email.includes('@') || !email.includes('.')) {
                    signupMessage.textContent = '请输入有效的邮箱地址';
                    signupMessage.className = 'auth-message error';
                    return;
                }

                if (!password) {
                    signupMessage.textContent = '请输入密码';
                    signupMessage.className = 'auth-message error';
                    return;
                }

                if (password.length < 6) {
                    signupMessage.textContent = '密码至少需要6位字符';
                    signupMessage.className = 'auth-message error';
                    return;
                }

                // 验证 Turnstile
                if (!turnstileToken) {
                    signupMessage.textContent = '请完成人机验证';
                    signupMessage.className = 'auth-message error';
                    return;
                }

                // 只有在需要邀请码时才验证
                if (systemConfig.requireInvitationCode && !inviteCode) {
                    signupMessage.textContent = '请输入邀请码';
                    signupMessage.className = 'auth-message error';
                    return;
                }

                signupMessage.textContent = '正在注册...';
                signupMessage.className = 'auth-message';

                try {
                    // 根据系统配置决定是否传递邀请码
                    const requestBody = { email, password, turnstile_token: turnstileToken };
                    if (systemConfig.requireInvitationCode) {
                        requestBody.invitation_code = inviteCode;
                    }

                    const { data, error } = await supabase.functions.invoke('sign-up-with-invite', {
                        body: requestBody
                    });



                    if (error) {
                        // 网络错误或函数调用失败
                        console.error('Edge Function 调用错误:', error);
                        throw new Error('注册失败，服务器连接错误，请稍后重试');
                    }

                    if (data && data.error) {
                        // 业务逻辑错误，现在应该能正确获取到了
                        const errorMsg = data.error.toLowerCase();

                        if (errorMsg.includes('无效的邀请码') || errorMsg.includes('invalid invitation')) {
                            throw new Error('注册失败，邀请码无效');
                        } else if (errorMsg.includes('已被使用') || errorMsg.includes('already used')) {
                            throw new Error('注册失败，邀请码已被使用');
                        } else if (errorMsg.includes('请输入邀请码')) {
                            throw new Error('注册失败，请输入邀请码');
                        } else if (errorMsg.includes('password') && (errorMsg.includes('short') || errorMsg.includes('weak') || errorMsg.includes('6'))) {
                            throw new Error('注册失败，密码至少需要6位字符');
                        } else if (errorMsg.includes('email') && errorMsg.includes('invalid')) {
                            throw new Error('注册失败，邮箱格式不正确');
                        } else if (errorMsg.includes('email') && (errorMsg.includes('already') || errorMsg.includes('exists'))) {
                            throw new Error('注册失败，该邮箱已被注册');
                        } else if (errorMsg.includes('user') && (errorMsg.includes('already') || errorMsg.includes('exists'))) {
                            throw new Error('注册失败，用户已存在');
                        } else if (errorMsg.includes('signup') && errorMsg.includes('disabled')) {
                            throw new Error('注册失败，注册功能已被禁用');
                        } else if (errorMsg.includes('rate limit') || errorMsg.includes('too many')) {
                            throw new Error('注册失败，请求过于频繁，请稍后重试');
                        } else {
                            // 显示原始错误信息，便于调试
                            throw new Error(`注册失败：${data.error}`);
                        }
                    }



                    // 注册成功，尝试自动登录
                    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({ email, password });
                    if (loginError) {
                        signupMessage.textContent = '注册成功，但自动登录失败，请手动登录';
                        signupMessage.className = 'auth-message warning';
                    } else {
                        showToast('注册成功！', 'success');
                        authModal.style.display = 'none';
                        clearAllAuthForms(); // 清空表单
                        updateUserUI(loginData.user);
                    }
                } catch (error) {
                    console.error('注册过程中的错误:', error);
                    signupMessage.textContent = error.message;
                    signupMessage.className = 'auth-message error';
                }
            });
        }

        const resetForm = document.getElementById('reset-form');
        if(resetForm) {
            resetForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                const email = document.getElementById('reset-email').value.trim();
                const resetMessage = document.getElementById('reset-message');

                // 前端基本验证
                if (!email) {
                    resetMessage.textContent = '请输入邮箱地址';
                    resetMessage.className = 'auth-message error';
                    return;
                }

                if (!email.includes('@') || !email.includes('.')) {
                    resetMessage.textContent = '请输入有效的邮箱地址';
                    resetMessage.className = 'auth-message error';
                    return;
                }

                const { error } = await supabase.auth.resetPasswordForEmail(email, { redirectTo: window.location.origin });
                if (error) {
                    const errorMsg = error.message.toLowerCase();

                    if (errorMsg.includes('user not found') || errorMsg.includes('email not found')) {
                        resetMessage.textContent = '该邮箱地址未注册，请检查邮箱地址';
                    } else if (errorMsg.includes('rate limit') || errorMsg.includes('too many')) {
                        resetMessage.textContent = '请求过于频繁，请稍后重试';
                    } else if (errorMsg.includes('invalid email')) {
                        resetMessage.textContent = '邮箱地址格式不正确';
                    } else {
                        resetMessage.textContent = `发送失败：${error.message}`;
                    }
                    resetMessage.className = 'auth-message error';
                } else {
                    resetMessage.textContent = '重置链接已发送，请检查您的邮箱';
                    resetMessage.className = 'auth-message success';
                }
            });
        }
    }

    async function handleLogout() {
        const { error } = await supabase.auth.signOut();
        if (error) {
            showToast('登出失败，请稍后重试', 'error');
        } else {
            showToast('已成功登出', 'success');
            updateUserUI(null);
        }
    }

    // --- Admin Functions ---
    function isAdmin(user) {
        return user?.user_metadata?.role === 'admin' ||
               user?.app_metadata?.role === 'admin';
    }

    function updateUserUI(user) {
        const loginBtn = getLoginBtn();
        const userInfo = getUserInfo();
        const contributeBtn = getContributeBtn();
        const avatarText = document.getElementById('avatar-text');
        const dropdownEmail = document.getElementById('dropdown-email');
        const adminConfigBtn = document.getElementById('admin-config-btn');

        if (user) {
            if(loginBtn) loginBtn.style.display = 'none';
            if(userInfo) userInfo.style.display = 'flex';
            if(contributeBtn) contributeBtn.style.display = 'block';

            // 更新头像文字（取邮箱第一个字符）
            if(avatarText && user.email) {
                avatarText.textContent = user.email.charAt(0).toUpperCase();
            }

            // 更新下拉菜单中的邮箱
            if(dropdownEmail) {
                dropdownEmail.textContent = user.email;
            }

            // 显示/隐藏管理员配置按钮
            if(adminConfigBtn) {
                adminConfigBtn.style.display = isAdmin(user) ? 'block' : 'none';
            }
        } else {
            if(loginBtn) loginBtn.style.display = 'block';
            if(userInfo) userInfo.style.display = 'none';
            if(contributeBtn) contributeBtn.style.display = 'none';

            // 清空头像和邮箱信息
            if(avatarText) avatarText.textContent = 'U';
            if(dropdownEmail) dropdownEmail.textContent = '';

            // 隐藏管理员配置按钮
            if(adminConfigBtn) {
                adminConfigBtn.style.display = 'none';
            }
        }
    }

    // --- User Avatar Dropdown Logic ---
    function setupUserDropdown() {
        const userAvatar = document.getElementById('user-avatar');
        const userDropdown = document.getElementById('user-dropdown');
        const changePasswordBtn = document.getElementById('change-password-btn');
        const changePasswordModal = document.getElementById('change-password-modal');
        const adminConfigBtn = document.getElementById('admin-config-btn');
        const adminConfigModal = document.getElementById('admin-config-modal');

        if (userAvatar && userDropdown) {
            // 点击头像切换下拉菜单
            userAvatar.addEventListener('click', (e) => {
                e.stopPropagation();
                userDropdown.classList.toggle('show');
            });

            // 点击页面其他地方关闭下拉菜单
            document.addEventListener('click', (e) => {
                if (!userAvatar.contains(e.target) && !userDropdown.contains(e.target)) {
                    userDropdown.classList.remove('show');
                }
            });

            // 阻止下拉菜单内部点击事件冒泡
            userDropdown.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }

        // 修改密码按钮事件
        if (changePasswordBtn && changePasswordModal) {
            changePasswordBtn.addEventListener('click', () => {
                userDropdown.classList.remove('show'); // 关闭下拉菜单
                changePasswordModal.style.display = 'flex';
                clearChangePasswordForm();
            });
        }

        // 管理员配置按钮事件
        if (adminConfigBtn && adminConfigModal) {
            adminConfigBtn.addEventListener('click', async () => {
                userDropdown.classList.remove('show'); // 关闭下拉菜单
                adminConfigModal.style.display = 'flex';
                await loadAdminConfig(); // 加载当前配置
            });
        }
    }

    // --- Change Password Logic ---
    function clearChangePasswordForm() {
        const currentPassword = document.getElementById('current-password');
        const newPassword = document.getElementById('new-password');
        const confirmPassword = document.getElementById('confirm-password');
        const changePasswordMessage = document.getElementById('change-password-message');

        if (currentPassword) currentPassword.value = '';
        if (newPassword) newPassword.value = '';
        if (confirmPassword) confirmPassword.value = '';
        if (changePasswordMessage) {
            changePasswordMessage.textContent = '';
            changePasswordMessage.className = 'auth-message';
        }
    }

    function setupChangePassword() {
        const changePasswordModal = document.getElementById('change-password-modal');
        const changePasswordCloseBtn = document.getElementById('change-password-close-btn');
        const changePasswordForm = document.getElementById('change-password-form');
        const forgotCurrentPasswordLink = document.getElementById('forgot-current-password');
        const resetConfirmationModal = document.getElementById('reset-confirmation-modal');

        // 关闭按钮事件
        if (changePasswordCloseBtn) {
            changePasswordCloseBtn.addEventListener('click', () => {
                changePasswordModal.style.display = 'none';
                clearChangePasswordForm();
            });
        }

        // 移除点击背景关闭模态框的功能，只能通过关闭按钮关闭

        // "忘记当前密码？"链接事件
        if (forgotCurrentPasswordLink && resetConfirmationModal) {
            forgotCurrentPasswordLink.addEventListener('click', async (e) => {
                e.preventDefault();

                // 获取当前用户邮箱
                const { data: { user } } = await supabase.auth.getUser();
                if (user && user.email) {
                    const resetEmailDisplay = document.getElementById('reset-email-display');
                    if (resetEmailDisplay) {
                        resetEmailDisplay.textContent = user.email;
                    }

                    // 显示重置确认对话框
                    resetConfirmationModal.style.display = 'flex';
                    clearResetConfirmationForm();
                } else {
                    showToast('获取用户信息失败，请重新登录', 'error');
                }
            });
        }

        // 修改密码表单提交
        if (changePasswordForm) {
            changePasswordForm.addEventListener('submit', async (e) => {
                e.preventDefault();

                const currentPassword = document.getElementById('current-password').value;
                const newPassword = document.getElementById('new-password').value.trim();
                const confirmPassword = document.getElementById('confirm-password').value.trim();
                const changePasswordMessage = document.getElementById('change-password-message');

                // 前端验证
                if (!currentPassword) {
                    changePasswordMessage.textContent = '请输入当前密码';
                    changePasswordMessage.className = 'auth-message error';
                    return;
                }

                if (!newPassword) {
                    changePasswordMessage.textContent = '请输入新密码';
                    changePasswordMessage.className = 'auth-message error';
                    return;
                }

                if (newPassword.length < 6) {
                    changePasswordMessage.textContent = '新密码至少需要6位字符';
                    changePasswordMessage.className = 'auth-message error';
                    return;
                }

                if (newPassword !== confirmPassword) {
                    changePasswordMessage.textContent = '两次输入的密码不一致';
                    changePasswordMessage.className = 'auth-message error';
                    return;
                }

                if (currentPassword === newPassword) {
                    changePasswordMessage.textContent = '新密码不能与当前密码相同';
                    changePasswordMessage.className = 'auth-message error';
                    return;
                }

                changePasswordMessage.textContent = '正在验证当前密码...';
                changePasswordMessage.className = 'auth-message';

                try {
                    // 首先获取当前用户信息
                    const { data: { user } } = await supabase.auth.getUser();
                    if (!user || !user.email) {
                        changePasswordMessage.textContent = '用户信息获取失败，请重新登录';
                        changePasswordMessage.className = 'auth-message error';
                        return;
                    }

                    // 使用当前密码重新认证来验证密码是否正确
                    const { error: signInError } = await supabase.auth.signInWithPassword({
                        email: user.email,
                        password: currentPassword
                    });

                    if (signInError) {
                        console.error('当前密码验证失败:', signInError);
                        if (signInError.message.includes('Invalid login credentials') ||
                            signInError.message.includes('Invalid email or password')) {
                            changePasswordMessage.textContent = '当前密码错误，请重新输入';
                        } else if (signInError.message.includes('Email not confirmed')) {
                            changePasswordMessage.textContent = '邮箱未确认，无法修改密码';
                        } else {
                            changePasswordMessage.textContent = '密码验证失败，请稍后重试';
                        }
                        changePasswordMessage.className = 'auth-message error';
                        return;
                    }

                    // 当前密码验证成功，开始更新密码
                    changePasswordMessage.textContent = '正在更新密码...';
                    changePasswordMessage.className = 'auth-message';

                    // 当前密码验证成功，现在更新密码
                    const { error: updateError } = await supabase.auth.updateUser({
                        password: newPassword
                    });

                    if (updateError) {
                        if (updateError.message.includes('New password should be different')) {
                            changePasswordMessage.textContent = '新密码不能与当前密码相同';
                        } else if (updateError.message.includes('Password should be at least')) {
                            changePasswordMessage.textContent = '密码至少需要6位字符';
                        } else {
                            changePasswordMessage.textContent = '修改密码失败，请稍后重试';
                        }
                        changePasswordMessage.className = 'auth-message error';
                    } else {
                        showToast('密码修改成功！', 'success');
                        changePasswordModal.style.display = 'none';
                        clearChangePasswordForm();

                        // 密码修改成功后，确保用户界面状态正确
                        const { data: { user: updatedUser } } = await supabase.auth.getUser();
                        updateUserUI(updatedUser);
                    }
                } catch (error) {
                    console.error('修改密码错误:', error);
                    changePasswordMessage.textContent = '修改密码失败，请稍后重试';
                    changePasswordMessage.className = 'auth-message error';
                }
            });
        }

        // 设置重置密码确认对话框
        setupResetConfirmation();
    }

    // --- Reset Password Confirmation Logic ---
    function clearResetConfirmationForm() {
        const resetConfirmationMessage = document.getElementById('reset-confirmation-message');
        if (resetConfirmationMessage) {
            resetConfirmationMessage.textContent = '';
            resetConfirmationMessage.className = 'auth-message';
        }
    }

    function setupResetConfirmation() {
        const resetConfirmationModal = document.getElementById('reset-confirmation-modal');
        const resetConfirmationCloseBtn = document.getElementById('reset-confirmation-close-btn');
        const cancelResetBtn = document.getElementById('cancel-reset-btn');
        const confirmResetBtn = document.getElementById('confirm-reset-btn');

        // 关闭按钮事件
        if (resetConfirmationCloseBtn) {
            resetConfirmationCloseBtn.addEventListener('click', () => {
                resetConfirmationModal.style.display = 'none';
                clearResetConfirmationForm();
            });
        }

        // 取消按钮事件
        if (cancelResetBtn) {
            cancelResetBtn.addEventListener('click', () => {
                resetConfirmationModal.style.display = 'none';
                clearResetConfirmationForm();
            });
        }

        // 确认重置按钮事件
        if (confirmResetBtn) {
            confirmResetBtn.addEventListener('click', async () => {
                const resetConfirmationMessage = document.getElementById('reset-confirmation-message');

                try {
                    // 获取当前用户邮箱
                    const { data: { user } } = await supabase.auth.getUser();
                    if (!user || !user.email) {
                        resetConfirmationMessage.textContent = '获取用户信息失败，请重新登录';
                        resetConfirmationMessage.className = 'auth-message error';
                        return;
                    }

                    resetConfirmationMessage.textContent = '正在发送重置邮件...';
                    resetConfirmationMessage.className = 'auth-message';

                    // 发送重置密码邮件
                    const { error } = await supabase.auth.resetPasswordForEmail(user.email, {
                        redirectTo: window.location.origin
                    });

                    if (error) {
                        const errorMsg = error.message.toLowerCase();

                        if (errorMsg.includes('user not found') || errorMsg.includes('email not found')) {
                            resetConfirmationMessage.textContent = '该邮箱地址未注册';
                        } else if (errorMsg.includes('rate limit') || errorMsg.includes('too many')) {
                            resetConfirmationMessage.textContent = '请求过于频繁，请稍后重试';
                        } else {
                            resetConfirmationMessage.textContent = `发送重置邮件失败：${error.message}`;
                        }
                        resetConfirmationMessage.className = 'auth-message error';
                    } else {
                        resetConfirmationMessage.textContent = '重置邮件已发送！请检查您的邮箱并点击重置链接。';
                        resetConfirmationMessage.className = 'auth-message success';

                        // 3秒后关闭对话框
                        setTimeout(() => {
                            resetConfirmationModal.style.display = 'none';
                            clearResetConfirmationForm();

                            // 同时关闭修改密码弹窗
                            const changePasswordModal = document.getElementById('change-password-modal');
                            if (changePasswordModal) {
                                changePasswordModal.style.display = 'none';
                                clearChangePasswordForm();
                            }

                            showToast('重置邮件已发送，请检查邮箱', 'success');
                        }, 3000);
                    }
                } catch (error) {
                    console.error('重置密码错误:', error);
                    resetConfirmationMessage.textContent = '发送重置邮件失败，请稍后重试';
                    resetConfirmationMessage.className = 'auth-message error';
                }
            });
        }
    }

    // --- Admin Configuration Logic ---
    async function loadAdminConfig() {
        const requireInvitationToggle = document.getElementById('require-invitation-toggle');
        const adminConfigMessage = document.getElementById('admin-config-message');

        try {
            adminConfigMessage.textContent = '正在加载配置...';
            adminConfigMessage.className = 'auth-message';

            const { data, error } = await supabase
                .from('system_config')
                .select('config_key, config_value')
                .eq('config_key', 'require_invitation_code')
                .single();

            if (error) {
                console.error('加载管理员配置失败:', error);
                adminConfigMessage.textContent = '加载配置失败，请稍后重试';
                adminConfigMessage.className = 'auth-message error';
                return;
            }

            // 设置复选框状态
            if (requireInvitationToggle) {
                requireInvitationToggle.checked = data.config_value === 'true';
            }

            adminConfigMessage.textContent = '';
            adminConfigMessage.className = 'auth-message';

        } catch (error) {
            console.error('加载管理员配置错误:', error);
            adminConfigMessage.textContent = '加载配置失败，请稍后重试';
            adminConfigMessage.className = 'auth-message error';
        }
    }

    function clearAdminConfigForm() {
        const adminConfigMessage = document.getElementById('admin-config-message');
        if (adminConfigMessage) {
            adminConfigMessage.textContent = '';
            adminConfigMessage.className = 'auth-message';
        }
    }

    function setupAdminConfig() {
        const adminConfigModal = document.getElementById('admin-config-modal');
        const adminConfigCloseBtn = document.getElementById('admin-config-close-btn');
        const saveConfigBtn = document.getElementById('save-config-btn');

        // 关闭按钮事件
        if (adminConfigCloseBtn) {
            adminConfigCloseBtn.addEventListener('click', () => {
                adminConfigModal.style.display = 'none';
                clearAdminConfigForm();
            });
        }

        // 保存配置按钮事件
        if (saveConfigBtn) {
            saveConfigBtn.addEventListener('click', async () => {
                const requireInvitationToggle = document.getElementById('require-invitation-toggle');
                const adminConfigMessage = document.getElementById('admin-config-message');

                if (!requireInvitationToggle) return;

                try {
                    adminConfigMessage.textContent = '正在保存配置...';
                    adminConfigMessage.className = 'auth-message';

                    const newValue = requireInvitationToggle.checked ? 'true' : 'false';

                    const { error } = await supabase
                        .from('system_config')
                        .update({
                            config_value: newValue,
                            updated_at: new Date().toISOString()
                        })
                        .eq('config_key', 'require_invitation_code');

                    if (error) {
                        console.error('保存配置失败:', error);
                        if (error.message.includes('permission') || error.message.includes('policy') || error.message.includes('RLS')) {
                            adminConfigMessage.textContent = '权限不足，无法修改配置。请检查管理员权限设置。';
                        } else {
                            adminConfigMessage.textContent = `保存配置失败：${error.message}`;
                        }
                        adminConfigMessage.className = 'auth-message error';
                        return;
                    }

                    // 更新本地配置
                    systemConfig.requireInvitationCode = requireInvitationToggle.checked;
                    updateRegistrationUI();

                    adminConfigMessage.textContent = '配置保存成功！';
                    adminConfigMessage.className = 'auth-message success';

                    showToast('系统配置已更新', 'success');

                    // 2秒后关闭模态框
                    setTimeout(() => {
                        adminConfigModal.style.display = 'none';
                        clearAdminConfigForm();
                    }, 2000);

                } catch (error) {
                    console.error('保存配置错误:', error);
                    adminConfigMessage.textContent = '保存配置失败，请稍后重试';
                    adminConfigMessage.className = 'auth-message error';
                }
            });
        }
    }

    // --- General Event Listeners ---
    function setupEventListeners() {
        const loadStreamBtn = getLoadStreamBtn();
        if(loadStreamBtn) {
            loadStreamBtn.addEventListener('click', () => {
                const m3uInput = getM3uInput();
                if (m3uInput) loadStream(m3uInput.value.trim());
            });
        }
        
        const m3uInput = getM3uInput();
        if(m3uInput) m3uInput.addEventListener('keypress', e => { if (e.key === 'Enter') loadStream(m3uInput.value.trim()); });
        
        const logoutBtn = getLogoutBtn();
        if(logoutBtn) logoutBtn.addEventListener('click', handleLogout);

        const sendDanmakuBtn = getSendDanmakuBtn();
        if(sendDanmakuBtn) sendDanmakuBtn.addEventListener('click', sendDanmaku);

        const danmakuInput = getDanmakuInput();
        if(danmakuInput) danmakuInput.addEventListener('keypress', e => { if (e.key === 'Enter') sendDanmaku(); });

        const contributeBtn = getContributeBtn();
        if(contributeBtn) contributeBtn.addEventListener('click', handleContributeStream);

        const streamDropdown = getStreamDropdown();
        if(streamDropdown) {
            streamDropdown.addEventListener('change', () => {
                const selectedUrl = streamDropdown.value;
                if (selectedUrl) {
                    const m3uInput = getM3uInput();
                    if(m3uInput) m3uInput.value = selectedUrl;
                    loadStream(selectedUrl);
                }
            });
        }

        const infoModal = getInfoModal();
        const scheduleBtn = getScheduleBtn();
        if(scheduleBtn && infoModal) {
            scheduleBtn.addEventListener('click', () => {
                infoModal.style.display = 'flex';
                fetchF1Data();
            });
            const infoModalCloseBtn = infoModal.querySelector('.auth-close-btn');
            if(infoModalCloseBtn) infoModalCloseBtn.addEventListener('click', () => infoModal.style.display = 'none');
            // 移除点击背景关闭模态框的功能，只能通过关闭按钮关闭
        }

        const modalTabs = document.querySelector('.modal-tabs');
        if(modalTabs) {
            modalTabs.addEventListener('click', e => {
                if (e.target.tagName === 'BUTTON') {
                    const activeTab = document.querySelector('.tab-link.active');
                    if(activeTab) activeTab.classList.remove('active');
                    const activeContent = document.querySelector('.tab-content.active');
                    if(activeContent) activeContent.classList.remove('active');
                    e.target.classList.add('active');
                    const tabContent = document.getElementById(e.target.dataset.tab);
                    if(tabContent) tabContent.classList.add('active');
                }
            });
        }

        const videoPlayer = getVideoPlayer();
        if(videoPlayer) {
            videoPlayer.addEventListener('dblclick', () => { 
                if (!document.fullscreenElement) videoPlayer.requestFullscreen(); 
                else document.exitFullscreen(); 
            });
        }

        document.addEventListener('keydown', e => {
            if (e.target.tagName.toLowerCase() === 'input') return;
            const video = getVideoPlayer();
            if(!video) return;
            switch (e.code) {
                case 'Space': e.preventDefault(); video.paused ? video.play() : video.pause(); break;
                case 'KeyF': e.preventDefault(); if (!document.fullscreenElement) video.requestFullscreen(); else document.exitFullscreen(); break;
                case 'ArrowUp': e.preventDefault(); video.volume = Math.min(1, video.volume + 0.1); break;
                case 'ArrowDown': e.preventDefault(); video.volume = Math.max(0, video.volume - 0.1); break;
                case 'ArrowRight': e.preventDefault(); video.currentTime += 5; break;
                case 'ArrowLeft': e.preventDefault(); video.currentTime -= 5; break;
            }
        });
    }

    // --- Initial Load ---
    async function initializeApp() {
        try {
            const { data: { session } } = await supabase.auth.getSession();
            updateUserUI(session?.user ?? null);
            supabase.auth.onAuthStateChange((_event, session) => updateUserUI(session?.user ?? null));
            
            const lastUrl = localStorage.getItem('lastStreamUrl');
            const m3uInput = getM3uInput();
            if (lastUrl && m3uInput) {
                m3uInput.value = lastUrl;
            }

            // 首先加载系统配置
            await loadSystemConfig();

            setupAuth();
            setupEventListeners();
            setupUserDropdown();
            setupChangePassword();
            setupAdminConfig();
            subscribeToDanmaku();
            fetchAndDisplayStreams();
        } catch (e) {
            console.error("初始化应用失败: ", e);
            showToast("应用加载失败，请检查控制台错误信息", 'error');
        }
    }

    initializeApp();
});