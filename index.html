<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>F1 Live Stream</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
</head>
<body>
    <header>
        <div class="logo">F1 Live Stream</div>
        <nav>
            <a href="#" id="schedule-btn">赛程/积分榜</a>
            <a href="#" id="contribute-btn" style="display: none;">贡献播放源</a>
        </nav>
        <div class="user-auth">
            <button id="login-btn">登录/注册</button>
            <div id="user-info" style="display: none;">
                <div class="user-avatar-container">
                    <div class="user-avatar" id="user-avatar">
                        <span class="avatar-text" id="avatar-text">U</span>
                    </div>
                    <div class="user-dropdown" id="user-dropdown">
                        <div class="dropdown-header">
                            <span id="dropdown-email"></span>
                        </div>
                        <div class="dropdown-divider"></div>
                        <button class="dropdown-item" id="change-password-btn">
                            <span class="dropdown-icon">🔑</span>
                            修改密码
                        </button>
                        <button class="dropdown-item" id="admin-config-btn" style="display: none;">
                            <span class="dropdown-icon">⚙️</span>
                            系统配置
                        </button>
                        <button class="dropdown-item" id="logout-btn">
                            <span class="dropdown-icon">🚪</span>
                            登出
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <main>
        <div class="video-container">
            <video id="video-player" controls></video>
            <div class="danmaku-overlay"></div>
        </div>

        <div class="controls-container">
            <div class="stream-selector">
                <select id="stream-dropdown">
                    <option value="">-- 选择社区线路 --</option>
                </select>
                <span>或</span>
                <input type="text" id="m3u-input" placeholder="粘贴你的 M3U/M3U8 链接...">
                <button id="load-stream-btn">加载</button>
            </div>
            <div class="danmaku-sender">
                <input type="text" id="danmaku-input" placeholder="发个弹幕见证历史...">
                <button id="send-danmaku-btn">发送</button>
            </div>
        </div>
    </main>

    <footer>
        <p>Powered by <a href="https://github.com" target="_blank">GitHub</a> & <a href="https://pages.cloudflare.com/" target="_blank">Cloudflare Pages</a>. MPV-style F1 Viewer.</p>
    </footer>

    <!-- Info Modal -->
    <div id="info-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <button class="auth-close-btn">&times;</button>
            <h2>F1 Information Center</h2>
            <div class="modal-tabs">
                <button class="tab-link active" data-tab="schedule">赛程</button>
                <button class="tab-link" data-tab="driver-standings">车手积分榜</button>
                <button class="tab-link" data-tab="constructor-standings">车队积分榜</button>
            </div>
            <div id="schedule" class="tab-content active">
                <h3>赛程 (Schedule)</h3>
                <div id="schedule-content"></div>
            </div>
            <div id="driver-standings" class="tab-content">
                <h3>车手积分榜 (Driver Standings)</h3>
                <div id="driver-standings-content"></div>
            </div>
            <div id="constructor-standings" class="tab-content">
                <h3>车队积分榜 (Constructor Standings)</h3>
                <div id="constructor-standings-content"></div>
            </div>
        </div>
    </div>

    <!-- Auth Modal -->
    <div id="auth-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content auth-modal-content">
            <button id="auth-close-btn" class="auth-close-btn">&times;</button>

            <!-- Login View -->
            <div id="login-view">
                <h2>欢迎回来</h2>
                <form id="login-form">
                    <input type="email" id="login-email" placeholder="邮箱地址" required>
                    <input type="password" id="login-password" placeholder="密码" required>
                    <p class="auth-message" id="login-message"></p>
                    <button type="submit">登录</button>
                </form>
                <p class="auth-switch">没有账户？ <a href="#" id="show-signup">立即注册</a></p>
                <p class="auth-switch"><a href="#" id="show-reset">忘记密码？</a></p>
            </div>

            <!-- Signup View -->
            <div id="signup-view" style="display: none;">
                <h2>创建账户</h2>
                <form id="signup-form">
                    <input type="email" id="signup-email" placeholder="邮箱地址" required>
                    <input type="password" id="signup-password" placeholder="密码" required>
                    <input type="text" id="signup-invite-code" placeholder="邀请码" required>
                    <div class="turnstile-container">
                        <div class="cf-turnstile" data-sitekey="0x4AAAAAABof2Gg619dti5bh" data-callback="onTurnstileSuccess" data-expired-callback="onTurnstileExpired" data-error-callback="onTurnstileError"></div>
                    </div>
                    <p class="auth-message" id="signup-message"></p>
                    <button type="submit">注册</button>
                </form>
                <p class="auth-switch">已有账户？ <a href="#" id="show-login">立即登录</a></p>
            </div>

            <!-- Reset Password View -->
            <div id="reset-view" style="display: none;">
                <h2>重置密码</h2>
                <form id="reset-form">
                    <input type="email" id="reset-email" placeholder="邮箱地址" required>
                    <p class="auth-message" id="reset-message"></p>
                    <button type="submit">发送重置链接</button>
                </form>
                <p class="auth-switch"><a href="#" id="show-login-from-reset">返回登录</a></p>
            </div>
        </div>
    </div>

    <!-- Change Password Modal -->
    <div id="change-password-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content auth-modal-content">
            <button id="change-password-close-btn" class="auth-close-btn">&times;</button>
            <h2>修改密码</h2>
            <form id="change-password-form">
                <input type="password" id="current-password" placeholder="当前密码" required>
                <p class="forgot-password-link">
                    <a href="#" id="forgot-current-password">忘记当前密码？</a>
                </p>
                <input type="password" id="new-password" placeholder="新密码（至少6位）" required>
                <input type="password" id="confirm-password" placeholder="确认新密码" required>
                <p class="auth-message" id="change-password-message"></p>
                <button type="submit">修改密码</button>
            </form>
        </div>
    </div>

    <!-- Reset Password Confirmation Modal -->
    <div id="reset-confirmation-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content auth-modal-content">
            <button id="reset-confirmation-close-btn" class="auth-close-btn">&times;</button>
            <h2>确认重置密码？</h2>
            <div class="reset-confirmation-content">
                <p>我们将向您的邮箱发送重置链接：</p>
                <p class="user-email-display" id="reset-email-display"></p>
                <p class="reset-warning">重置完成后请重新登录。</p>
                <p class="auth-message" id="reset-confirmation-message"></p>
                <div class="reset-confirmation-buttons">
                    <button type="button" id="cancel-reset-btn" class="secondary-btn">取消</button>
                    <button type="button" id="confirm-reset-btn" class="primary-btn">发送重置邮件</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Configuration Modal -->
    <div id="admin-config-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content auth-modal-content">
            <button id="admin-config-close-btn" class="auth-close-btn">&times;</button>
            <h2>系统配置</h2>
            <div class="admin-config-content">
                <div class="config-item">
                    <label class="config-label">
                        <input type="checkbox" id="require-invitation-toggle">
                        需要邀请码注册
                    </label>
                    <p class="config-description">开启后，用户注册时必须输入有效的邀请码</p>
                </div>
                <p class="auth-message" id="admin-config-message"></p>
                <button type="button" id="save-config-btn" class="primary-btn">保存配置</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>