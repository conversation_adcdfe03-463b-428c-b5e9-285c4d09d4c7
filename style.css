/* --- 全局与根变量 --- */
:root {
    --primary-bg: #121212;
    --secondary-bg: #1e1e1e;
    --tertiary-bg: #2a2a2a;
    --primary-text: #e0e0e0;
    --secondary-text: #a0a0a0;
    --accent-color: #e10600; /* F1 Red */
    --accent-hover: #ff3333;
    --border-color: #333;
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
}

body {
    background-color: var(--primary-bg);
    color: var(--primary-text);
    font-family: var(--font-family);
    margin: 0;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

a {
    color: var(--accent-color);
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* --- Header --- */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background-color: var(--secondary-bg);
    border-bottom: 1px solid var(--border-color);
}

header .logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--accent-color);
}

header nav a {
    margin: 0 1rem;
    color: var(--primary-text);
    font-weight: 500;
}

header nav a:hover {
    color: var(--accent-hover);
    text-decoration: none;
}

/* --- User Authentication Area --- */
.user-auth {
    display: flex;
    align-items: center;
    gap: 1rem;
}

#user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
}

/* --- User Avatar and Dropdown --- */
.user-avatar-container {
    position: relative;
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.user-avatar:hover {
    border-color: var(--accent-color);
    transform: scale(1.05);
}

.avatar-text {
    color: white;
    font-weight: bold;
    font-size: 14px;
    text-transform: uppercase;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    background-color: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-header {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
}

.dropdown-header span {
    color: var(--primary-text);
    font-size: 0.9rem;
    font-weight: 500;
    word-break: break-all;
}

.dropdown-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: 4px 0;
}

.dropdown-item {
    width: 100%;
    padding: 12px 16px;
    background: none;
    border: none;
    color: var(--primary-text);
    font-size: 0.9rem;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.dropdown-item:hover {
    background-color: var(--tertiary-bg);
}

.dropdown-icon {
    font-size: 16px;
}

#login-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: background-color 0.2s ease;
    white-space: nowrap;
}

#login-btn:hover {
    background-color: var(--accent-hover);
}

/* --- Main Content --- */
main {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    width: 100%;
    max-width: 1200px; /* 限制最大宽度 */
    margin: 0 auto;
    box-sizing: border-box;
}

.video-container {
    position: relative;
    width: 100%;
    background-color: #000;
    margin-bottom: 1rem;
}

#video-player {
    width: 100%;
    height: auto;
    display: block;
}

.danmaku-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

/* --- Controls --- */
.controls-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
    padding: 1rem;
    background-color: var(--secondary-bg);
    border-radius: 8px;
    box-sizing: border-box;
}

.stream-selector, .danmaku-sender {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

input[type="text"], select {
    flex: 1;
    padding: 0.75rem;
    background-color: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    color: var(--primary-text);
    border-radius: 4px;
    font-size: 1rem;
}

input[type="text"]::placeholder {
    color: var(--secondary-text);
}

button {
    padding: 0.75rem 1.5rem;
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: bold;
    transition: background-color 0.2s ease;
}

button:hover {
    background-color: var(--accent-hover);
}

/* --- Footer --- */
footer {
    text-align: center;
    padding: 1rem;
    background-color: var(--secondary-bg);
    border-top: 1px solid var(--border-color);
    color: var(--secondary-text);
    font-size: 0.9rem;
    width: 100%;
    box-sizing: border-box;
}

/* --- Danmaku Styles --- */
.danmaku {
    position: absolute;
    right: 0;
    transform: translateX(100%);
    white-space: nowrap;
    padding: 5px 10px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    font-size: 1.2rem;
    border-radius: 4px;
    animation: scroll-left 10s linear forwards;
    pointer-events: none; /* So they don't interfere with video controls */
}

@keyframes scroll-left {
    from {
        right: 0;
        transform: translateX(100%);
    }
    to {
        right: 100%;
        transform: translateX(0%);
    }
}

/* --- Modal Styles --- */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--secondary-bg);
    padding: 2rem;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

/* 旧的通用关闭按钮样式已被 .auth-close-btn 替代 */

.modal-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1rem;
}

.tab-link {
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    cursor: pointer;
    color: var(--secondary-text);
    font-size: 1rem;
}

.tab-link.active {
    color: var(--accent-color);
    border-bottom: 2px solid var(--accent-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* --- Table Styles for Modal --- */
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

thead {
    background-color: var(--tertiary-bg);
}

th, td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

tr:last-child td {
    border-bottom: none;
}

/* --- Auth Modal Styles --- */
.auth-modal-content {
    max-width: 420px;
    text-align: center;
    position: relative; /* 确保关闭按钮相对于此容器定位 */
}

.auth-modal-content h2 {
    color: var(--primary-text);
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    color: var(--secondary-text);
    margin-top: 0;
    margin-bottom: 1.5rem;
}

.auth-modal-content form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1rem;
}

.auth-modal-content input {
    width: 100%;
    padding: 0.8rem;
    background-color: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    color: var(--primary-text);
    border-radius: 4px;
    font-size: 1rem;
    box-sizing: border-box;
}

.auth-modal-content button[type="submit"],
.auth-modal-content .dropdown-item {
    width: 100%;
    padding: 0.8rem;
}

/* 关闭按钮不应该占满整行 */
.auth-modal-content .auth-close-btn {
    width: 32px;
    height: 32px;
    padding: 0;
}

.auth-message {
    color: var(--accent-hover);
    min-height: 20px;
    text-align: left;
    font-size: 0.9rem;
}

.auth-switch {
    margin-top: 1.5rem;
    color: var(--secondary-text);
}

.auth-switch a {
    font-weight: bold;
    cursor: pointer;
}

/* 统一的关闭按钮样式 */
.auth-close-btn {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    width: 32px;
    height: 32px;
    background-color: var(--tertiary-bg);
    color: var(--primary-text);
    border: none;
    border-radius: 6px;
    font-size: 1.2rem;
    line-height: 32px; /* Vertically center the '×' */
    text-align: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
    z-index: 10;
    padding: 0;
}

.auth-close-btn:hover {
    background-color: #4a4a4a; /* Darker background on hover */
}

/* 保持向后兼容 */
#auth-close-btn {
    /* 继承 .auth-close-btn 的样式 */
}

/* --- Toast Notifications --- */
.toast {
    position: fixed;
    top: 80px; /* 下移，避免与 header 重叠 */
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    font-size: 14px;
    z-index: 10000;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    max-width: 300px;
    word-wrap: break-word;
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

/* 响应式设计：移动设备 */
@media (max-width: 768px) {
    /* Header 响应式 */
    header {
        padding: 0.75rem 1rem;
        /* 保持 flex 单行布局 */
    }

    header .logo {
        font-size: 1.2rem;
    }

    header nav a {
        margin: 0 0.5rem;
        font-size: 0.85rem;
    }

    /* 用户信息区域 */
    .user-auth {
        gap: 0.5rem;
    }

    .user-avatar {
        width: 32px;
        height: 32px;
    }

    .avatar-text {
        font-size: 12px;
    }

    .user-dropdown {
        min-width: 180px;
        right: -10px;
    }

    .dropdown-header span {
        font-size: 0.8rem;
    }

    .dropdown-item {
        padding: 10px 12px;
        font-size: 0.8rem;
    }

    .dropdown-icon {
        font-size: 14px;
    }

    #login-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }

    /* Toast 响应式 */
    .toast {
        top: 70px;
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100px);
    }

    .toast.show {
        transform: translateY(0);
    }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
    header {
        padding: 0.5rem 0.75rem;
    }

    header .logo {
        font-size: 1rem;
    }

    header nav a {
        margin: 0 0.25rem;
        font-size: 0.8rem;
    }

    .user-avatar {
        width: 28px;
        height: 28px;
    }

    .avatar-text {
        font-size: 11px;
    }

    #login-btn {
        padding: 0.35rem 0.6rem;
        font-size: 0.75rem;
    }

    /* 重置密码确认对话框移动端适配 */
    .reset-confirmation-buttons {
        flex-direction: column;
        gap: 0.75rem;
    }

    .secondary-btn, .primary-btn {
        padding: 0.6rem 1rem;
        font-size: 0.85rem;
    }

    .forgot-password-link {
        text-align: center;
    }

    .forgot-password-link a {
        font-size: 0.8rem;
    }
}

.toast-success {
    background-color: var(--success-color);
}

.toast-error {
    background-color: var(--error-color);
}

.toast-warning {
    background-color: var(--warning-color);
}

.toast-info {
    background-color: var(--accent-color);
}

/* --- Auth Message Styles --- */
.auth-message {
    margin: 10px 0;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    text-align: center;
}

.auth-message.error {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(244, 67, 54, 0.3);
}

.auth-message.warning {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(255, 152, 0, 0.3);
}

.auth-message.success {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(76, 175, 80, 0.3);
}

/* --- Forgot Password Link --- */
.forgot-password-link {
    text-align: right;
    margin: -0.5rem 0 1rem 0;
}

.forgot-password-link a {
    color: var(--accent-color);
    font-size: 0.85rem;
    text-decoration: none;
    transition: color 0.2s ease;
}

.forgot-password-link a:hover {
    color: var(--accent-hover);
    text-decoration: underline;
}

/* --- Reset Password Confirmation Modal --- */
.reset-confirmation-content {
    text-align: center;
    padding: 1rem 0;
}

.reset-confirmation-content p {
    margin: 0.5rem 0;
    color: var(--primary-text);
}

.user-email-display {
    font-weight: bold;
    color: var(--accent-color);
    background-color: var(--tertiary-bg);
    padding: 0.5rem;
    border-radius: 4px;
    margin: 1rem 0;
}

.reset-warning {
    font-size: 0.9rem;
    color: var(--secondary-text);
    font-style: italic;
}

.reset-confirmation-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
    justify-content: center;
}

.secondary-btn, .primary-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 100px;
}

.secondary-btn {
    background-color: var(--tertiary-bg);
    color: var(--primary-text);
    border: 1px solid var(--border-color);
}

.secondary-btn:hover {
    background-color: var(--border-color);
}

.primary-btn {
    background-color: var(--accent-color);
    color: white;
}

.primary-btn:hover {
    background-color: var(--accent-hover);
}

/* --- Admin Configuration Modal --- */
.admin-config-content {
    padding: 1rem 0;
}

.config-item {
    margin: 1.5rem 0;
    padding: 1rem;
    background-color: var(--tertiary-bg);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.config-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
    color: var(--primary-text);
    cursor: pointer;
    font-size: 1rem;
}

.config-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--accent-color);
    cursor: pointer;
}

.config-description {
    margin: 0.75rem 0 0 2.25rem;
    font-size: 0.85rem;
    color: var(--secondary-text);
    line-height: 1.4;
}

#save-config-btn {
    width: 100%;
    margin-top: 1rem;
}

/* --- Turnstile Verification --- */
.turnstile-container {
    margin: 1rem 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.cf-turnstile {
    margin: 0 auto;
}

/* 暗色主题下的 Turnstile 样式调整 */
.turnstile-container iframe {
    border-radius: 4px;
}
