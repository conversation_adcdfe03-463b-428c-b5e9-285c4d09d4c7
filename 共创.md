基于我们的聊天记录，我来撰写完整的共创播放源产品方案。让我先回顾一下我们讨论的关键内容。

# 共创播放源产品方案

## 📋 产品概述

### 产品定位
基于F1 Live Stream平台的社区驱动播放源共创系统，通过用户贡献、社区审核、智能管理的方式，构建高质量、自运转的播放源生态。

### 核心价值
- **社区自治**：48小时内≥3人认定有效源自动上架，减少管理员干预
- **质量保障**：先测试后投票的审核机制，确保播放源有效性
- **简单易用**：最小化用户操作成本，提升参与积极性

## 🏗️ 系统架构

### 权限体系
```
未登录用户 → 登录用户 → 管理员用户
    ↓           ↓           ↓
  只能手动输入  可提交+投票   全权限管理
```

### 状态流转
```
用户提交 → pending(待审核) → approved(已通过) → expired(已失效)
                ↓                ↓              ↓
            社区投票≥3票        社区举报≥5票    可重新提交
            管理员直接批准      管理员直接下架   管理员恢复
```

## 📱 产品页面结构

### 1. 主播放页面（现有）
**页面组成：**
- Header：Logo + 导航（赛程/积分榜、贡献播放源）+ 用户系统
- Main：视频播放器 + 播放源选择 + 弹幕系统
- Footer：版权信息

**权限控制：**
- 未登录：只显示手动输入框，社区播放源下拉菜单隐藏
- 登录用户：显示社区播放源下拉菜单，显示"贡献播放源"按钮
- 管理员：额外显示"管理后台"入口

### 2. 播放源审核页面（新增）
**访问方式：**
- 登录用户点击"审核播放源"按钮
- 显示待审核播放源列表

**页面功能：**
- 待审核源列表展示
- 一键测试播放功能
- 投票界面（有效/无效）
- 投票统计显示

### 3. 管理后台页面（新增）
**访问权限：**仅管理员可见

**功能模块：**
- 播放源管理（待审核/已通过/已失效）
- 用户管理
- 系统配置
- 数据统计

### 4. 贡献排行榜页面（新增）
**展示内容：**
- 贡献者排行榜
- 贡献统计
- 质量评分

## ⚙️ 核心功能详解

### 1. 播放源提交系统

**提交流程：**
```javascript
用户点击"贡献播放源" → 填写表单 → 提交到pending状态 → 等待社区审核
```

**表单字段：**
- 播放源名称（必填）
- 播放源URL（必填，自动验证格式）
- 描述标签（可选，如"高清"、"备用线路"）

**验证逻辑：**
- URL格式验证（必须包含.m3u/.m3u8）
- 重复源检测
- 基础连通性测试

### 2. 社区审核系统

**审核触发：**
- 用户在审核页面看到待审核源列表
- 点击"测试播放"按钮

**测试流程：**
```javascript
点击测试 → 弹出播放模态框 → 加载播放源 → 用户观看10-15秒 → 投票有效/无效
```

**投票逻辑：**
- 每个用户对每个播放源只能投票一次
- 投票类型：valid（有效）、invalid（无效）
- 实时更新投票统计

**自动审核规则：**
- 48小时内≥3票"有效" → 自动变为approved
- 48小时内≥3票"无效" → 自动删除
- 48小时后仍<3票 → 保持pending状态

### 3. 播放源管理系统

**状态管理：**
```sql
-- 播放源状态
pending   - 待审核（用户不可见）
approved  - 已通过（用户可见可使用）
expired   - 已失效（用户不可见）
```

**自动下架逻辑：**
- approved状态的源被≥5人举报失效 → 自动变为expired
- 管理员可直接下架任何源

**恢复机制：**
- expired状态的源可重新提交（重置为pending）
- 管理员可直接恢复expired源为approved

### 4. 用户投票系统

**投票类型：**
- 审核投票：valid/invalid（针对pending状态）
- 举报投票：report_expired（针对approved状态）

**防刷机制：**
- 每个用户对每个播放源的每种投票类型只能投一次
- 数据库唯一约束：UNIQUE(stream_id, user_id, vote_type)

**投票统计：**
- 实时更新播放源的投票计数
- 触发器自动维护计数准确性

### 5. 排行榜系统

**排行规则（质量加权方案）：**
```sql
最终得分 = 贡献数量 × (1 + 平均质量分)
平均质量分 = 有效投票数 / (有效投票数 + 无效投票数)
```

**展示内容：**
- 用户邮箱前缀
- 贡献播放源数量
- 平均质量评分
- 综合得分排名

## 🗄️ 数据库设计

### 核心表结构

```sql
-- 播放源表（扩展现有streams表）
ALTER TABLE streams ADD COLUMN valid_votes INT DEFAULT 0;
ALTER TABLE streams ADD COLUMN invalid_votes INT DEFAULT 0;
ALTER TABLE streams ADD COLUMN report_votes INT DEFAULT 0;
ALTER TABLE streams ADD COLUMN submitted_at TIMESTAMP DEFAULT NOW();
ALTER TABLE streams ADD COLUMN auto_approved_at TIMESTAMP;
ALTER TABLE streams ADD COLUMN expired_at TIMESTAMP;

-- 投票记录表
CREATE TABLE stream_votes (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    stream_id BIGINT REFERENCES streams(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    vote_type TEXT CHECK (vote_type IN ('valid', 'invalid', 'report_expired')),
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(stream_id, user_id, vote_type)
);

-- 自动审核配置表
CREATE TABLE auto_review_config (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    config_key TEXT UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 权限控制（RLS策略）

```sql
-- 播放源读取权限
CREATE POLICY "Users can read approved streams" ON streams
FOR SELECT USING (status = 'approved' OR auth.uid() = submitter_id);

-- 投票权限
CREATE POLICY "Authenticated users can vote" ON stream_votes
FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- 管理员权限
CREATE POLICY "Admins can manage all streams" ON streams
FOR ALL USING (auth.jwt() ->> 'role' = 'admin');
```

## 🔄 业务流程

### 1. 播放源生命周期

```mermaid
graph TD
    A[用户提交] --> B[pending状态]
    B --> C{48小时内投票}
    C -->|≥3票有效| D[approved状态]
    C -->|≥3票无效| E[删除记录]
    C -->|<3票| F[保持pending]
    D --> G{用户举报}
    G -->|≥5票失效| H[expired状态]
    H --> I[可重新提交]
    I --> B
```

### 2. 用户审核流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant S as 系统
    participant V as 视频播放器
    participant D as 数据库
    
    U->>S: 点击"测试播放"
    S->>V: 创建播放模态框
    V->>V: 加载播放源
    V->>U: 显示播放内容
    U->>U: 观看10-15秒
    U->>S: 点击"有效"或"无效"
    S->>D: 记录投票
    D->>D: 更新投票统计
    D->>D: 检查自动审核条件
    S->>U: 显示投票成功
```

### 3. 自动化处理

**定时任务（每小时执行）：**
- 检查pending源是否达到自动通过条件
- 检查approved源是否达到自动失效条件
- 清理超时的待审核源

**实时触发器：**
- 投票后自动更新计数
- 达到阈值时自动变更状态
- 发送通知给相关用户

## 📊 产品指标

### 核心指标
- **播放源质量**：有效源占比、平均投票通过率
- **社区活跃度**：日活跃审核用户数、投票参与率
- **自动化效率**：自动审核占比、管理员干预频率

### 用户体验指标
- **审核效率**：平均审核时长、48小时内通过率
- **播放成功率**：approved源的实际可用率
- **用户满意度**：投票准确性、举报响应速度

## 🚀 实施计划

### 第一阶段：基础功能（2周）
- 扩展数据库表结构
- 实现播放源提交功能
- 开发基础审核界面

### 第二阶段：审核系统（2周）
- 实现测试播放模态框
- 开发投票功能
- 添加自动审核逻辑

### 第三阶段：管理功能（1周）
- 完善管理后台
- 添加排行榜功能
- 优化用户体验

### 第四阶段：优化完善（1周）
- 性能优化
- 移动端适配
- 用户反馈收集

## 💡 产品亮点

1. **社区自治**：最小化管理员工作量，让社区自己维护播放源质量
2. **质量保障**：先测试后投票，确保投票的准确性和有效性
3. **简单易用**：一键测试、一键投票，降低用户参与门槛
4. **智能管理**：自动审核、自动下架，系统自动运转
5. **激励机制**：排行榜系统鼓励用户积极参与贡献

这个方案既保证了播放源的质量，又实现了社区的自我管理，是一个可持续发展的共创生态系统。
